from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.encoders import ENCODERS_BY_TYPE
from fastapi_health import health
from pydantic_core import PydanticUndefinedType

from services.base.api.exception_handlers import set_default_exception_handlers
from services.base.api.health_endpoints import server_run_time
from services.base.api.middleware.cors_middleware import add_cors_middleware
from services.base.api.set_logging import set_uvicorn_logging
from services.base.dependency_bootstrapper import resource_cleanup
from services.base.telemetry.fastapi_instrumentor import instrument_app
from services.base.telemetry.telemetry_instrumentor import TelemetryInstrumentor
from services.mobile_service.api.loading_endpoints import loading_router
from settings.app_config import settings
from settings.app_constants import RUN_ENV_LOCAL, RUN_ENV_PRODUCTION
from settings.app_secrets import secrets

# TODO: Fixes issues with list query params until [https://github.com/tiangolo/fastapi/discussions/10331] is resolved
ENCODERS_BY_TYPE[PydanticUndefinedType] = lambda o: None

# telemetry
set_uvicorn_logging()
TelemetryInstrumentor.initialize(service_name="mobile_service", settings=settings, secrets=secrets)


@asynccontextmanager
async def lifespan(_: FastAPI):
    # startup
    yield

    # shutdown
    await resource_cleanup()


app = FastAPI(
    root_path=None if settings.RUN_ENV == RUN_ENV_LOCAL else "/mobile",
    openapi_url="/openapi.json" if not settings.RUN_ENV == RUN_ENV_PRODUCTION else None,
    title="Mobile service",
    version="0.2",
    lifespan=lifespan,
)
instrument_app(app=app, settings=settings)

set_default_exception_handlers(app)
add_cors_middleware(app=app)
# routes
app.add_api_route("/health", health([server_run_time]), response_model=dict[str, str])
app.include_router(loading_router)
