from uuid import UUI<PERSON>

from fastapi import API<PERSON><PERSON><PERSON>, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.exceptions import (
    BadR<PERSON><PERSON><PERSON>xception,
    DuplicateDocumentsFound,
    Incorrect<PERSON>perationException,
    NoContentException,
)
from services.data_service.api.constants import DataServicePrefixes, UseCaseEndpointRoutes
from services.data_service.api.models.request.use_case.insert_use_case_api_request_input import (
    InsertUseCaseAPIRequestInput,
)
from services.data_service.api.models.request.use_case.search_use_case_request_input import SearchUseCaseRequestInput
from services.data_service.api.models.request.use_case.update_use_case_api_request_input import (
    UpdateUseCaseAPIRequestInput,
)
from services.data_service.api.models.response.use_case.use_case_api_output import UseCaseAPIOutput
from services.data_service.application.use_cases.use_case.archive_use_case import ArchiveUseCase
from services.data_service.application.use_cases.use_case.insert_use_case import InsertUseCase
from services.data_service.application.use_cases.use_case.models.insert_use_case_input_boundary import (
    InsertUseCaseInputBoundary,
)
from services.data_service.application.use_cases.use_case.models.search_use_case_input_boundary import (
    SearchUseCaseInputBoundary,
)
from services.data_service.application.use_cases.use_case.models.update_use_case_input_boundary import (
    UpdateUseCaseInputBoundary,
)
from services.data_service.application.use_cases.use_case.search_use_case import SearchUseCase
from services.data_service.application.use_cases.use_case.update_use_case import UpdateUseCase

use_case_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.USE_CASE_PREFIX}",
    tags=["use case"],
    responses={404: {"description": "Not found"}},
)


@use_case_router.post(
    UseCaseEndpointRoutes.SEARCH,
    summary="Search Use Cases",
    description="""
    Search and retrieve use cases with advanced filtering, sorting, and pagination capabilities.

    This endpoint allows users to search through their use cases using various filters,
    sorting options, and pagination. Use cases represent specific scenarios or workflows
    that users want to track and manage in their personal data collection.

    **Key Features:**
    - **Advanced Filtering**: Query by name, tags, archived status, and other properties
    - **Flexible Sorting**: Sort by any use case field (created_at, name, updated_at, etc.)
    - **Pagination**: Use limit and continuation_token for efficient data retrieval
    - **Boolean Queries**: Combine multiple filters with AND/OR logic

    **Query Parameters:**
    - `limit`: Maximum number of results to return (1-10000, default: 100)
    - `continuation_token`: Token for pagination to get next page of results

    **Request Body:**
    - `sort`: Sorting configuration with field name and order
    - `query`: Advanced query filters using boolean logic and field-specific conditions
    """,
    response_description="List of use cases matching the search criteria",
    responses={
        200: {
            "description": "Use cases found and returned successfully",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "doc_id": "123e4567-e89b-12d3-a456-************",
                                "name": "Daily Workout Routine",
                                "type": "use_case",
                                "tags": ["fitness", "daily", "routine"],
                                "archived_at": None,
                                "system_properties": {
                                    "created_at": "2024-01-15T08:30:00Z",
                                    "updated_at": "2024-01-15T08:30:00Z",
                                },
                            }
                        ]
                    }
                }
            },
        },
        204: {"description": "No use cases found matching the search criteria"},
        400: {"description": "Invalid search parameters or malformed request"},
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request parameters"},
    },
)
async def search_use_cases_endpoint(
    use_case: SearchUseCase = Injected(SearchUseCase),
    input_boundary: SearchUseCaseInputBoundary = Depends(SearchUseCaseRequestInput.to_input_boundary),
) -> CommonDocumentsResponse[UseCaseAPIOutput]:
    use_cases = await use_case.execute_async(input_boundary=input_boundary)
    if not use_cases:
        raise NoContentException(message="no matching use_cases found")

    output = [UseCaseAPIOutput.map(model=uc) for uc in use_cases]

    return CommonDocumentsResponse[UseCaseAPIOutput](documents=output)


@use_case_router.patch(
    UseCaseEndpointRoutes.ARCHIVE,
    summary="Archive Use Cases",
    description="""
    Archive use cases to remove them from active use while preserving their data and history.

    This endpoint allows you to archive one or more use cases, marking them as inactive without
    permanently deleting them. Archived use cases are excluded from search results by default
    but can still be retrieved if specifically requested, and their data remains intact for
    historical reference.

    **Key Features:**
    - **Soft Deletion**: Use cases are marked as archived, not permanently deleted
    - **Data Preservation**: All use case data, metadata, and relationships are preserved
    - **Search Exclusion**: Archived use cases are excluded from default search results
    - **Batch Processing**: Multiple use cases can be archived in a single operation
    - **Atomic Operations**: All use cases in the request are archived together or the operation fails

    **Query Parameters:**
    - `use_case_ids`: Array of UUID strings identifying the use cases to archive
    """,
    response_description="Successfully archived use cases with updated metadata",
    responses={
        200: {
            "description": "Use cases successfully archived",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "doc_id": "123e4567-e89b-12d3-a456-************",
                                "name": "Old Workout Routine",
                                "type": "use_case",
                                "tags": ["fitness", "outdated"],
                                "archived_at": "2024-01-15T10:30:00Z",
                                "system_properties": {
                                    "created_at": "2024-01-01T08:30:00Z",
                                    "updated_at": "2024-01-15T10:30:00Z",
                                },
                            }
                        ]
                    }
                }
            },
        },
        400: {"description": "Invalid use case IDs or operation not allowed"},
        401: {"description": "Authentication required"},
        404: {"description": "One or more use cases not found"},
        422: {"description": "Validation error in query parameters"},
    },
)
async def archive_use_cases_endpoint(
    use_case_ids: list[UUID] = Query(..., description="List of use case IDs to archive"),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: ArchiveUseCase = Injected(ArchiveUseCase),
) -> CommonDocumentsResponse[UseCaseAPIOutput]:
    try:
        use_cases = await use_case.execute_async(owner_id=owner_id, use_case_ids=use_case_ids)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[UseCaseAPIOutput](
        documents=[UseCaseAPIOutput(**uc.model_dump(by_alias=True)) for uc in use_cases]
    )


@use_case_router.patch(
    UseCaseEndpointRoutes.BASE,
    summary="Update Use Cases",
    description="""
    Update existing use cases with new information while preserving their identity and system properties.

    This endpoint allows users to modify one or more existing use cases in their personal collection.
    Users can update properties such as name and tags while preserving the use case's identity
    and system-managed properties. Each use case to update must include its doc_id for identification.

    **Key Features:**
    - **Bulk Updates**: Modify multiple use cases in a single request
    - **Partial Updates**: Only specified fields are updated, others remain unchanged
    - **Identity Preservation**: Use case IDs remain unchanged during updates
    - **Duplicate Detection**: Prevents updates that would create duplicate use cases
    - **Atomic Operations**: All updates in a batch succeed or fail together

    **Request Body:**
    - `documents`: Array of use case update objects (minimum 1 required)
      - `doc_id`: UUID of the existing use case to update (required)
      - `name`: New name for the use case (required, minimum 1 character)
      - `tags`: Array of tag strings (optional, maximum defined by system limits)
    """,
    response_description="Successfully updated use cases with modified properties",
    responses={
        200: {
            "description": "Use cases successfully updated",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "doc_id": "123e4567-e89b-12d3-a456-************",
                                "name": "Updated Workout Routine",
                                "type": "use_case",
                                "tags": ["fitness", "updated", "routine"],
                                "archived_at": None,
                                "system_properties": {
                                    "created_at": "2024-01-01T08:30:00Z",
                                    "updated_at": "2024-01-15T10:30:00Z",
                                },
                            }
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - duplicate use cases, validation errors, or operation not allowed",
            "content": {
                "application/json": {
                    "examples": [
                        {
                            "summary": "Duplicate use cases",
                            "value": {"detail": "use case duplicates found in the update payload"},
                        },
                        {
                            "summary": "Validation error",
                            "value": {"detail": "Invalid use case data: doc_id not found"},
                        },
                    ]
                }
            },
        },
        401: {"description": "Authentication required"},
        404: {"description": "One or more use cases not found"},
        422: {"description": "Validation error in request body"},
    },
)
async def update_use_cases_endpoint(
    input_boundary: UpdateUseCaseInputBoundary = Depends(UpdateUseCaseAPIRequestInput.to_input_boundary),
    use_case: UpdateUseCase = Injected(UpdateUseCase),
) -> CommonDocumentsResponse[UseCaseAPIOutput]:
    try:
        use_cases = await use_case.execute_async(input_boundary=input_boundary)
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="use case duplicates found in the update payload") from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[UseCaseAPIOutput](
        documents=[UseCaseAPIOutput(**p.model_dump(by_alias=True)) for p in use_cases]
    )


@use_case_router.post(
    UseCaseEndpointRoutes.BASE,
    summary="Create Use Cases",
    description="""
    Create new use cases in the system for tracking scenarios, workflows, and patterns.

    This endpoint allows users to insert one or more new use cases into their personal data collection.
    Use cases represent specific scenarios, workflows, or patterns that users want to track and manage.
    Each use case must include a name and can optionally include tags for categorization and organization.

    **Key Features:**
    - **Bulk Creation**: Create multiple use cases in a single request
    - **Duplicate Detection**: Prevents creation of identical use cases
    - **Automatic ID Generation**: Each use case is assigned a unique identifier
    - **Tag Support**: Custom tags for organization and categorization
    - **User Association**: Use cases are linked to the authenticated user's account

    **Request Body:**
    - `documents`: Array of use case objects to create (minimum 1 required)
      - `name`: Name of the use case (required, minimum 1 character)
      - `tags`: Array of tag strings for categorization (optional, maximum defined by system limits)

    **Common Use Cases:**
    - Track daily routines and habits
    - Define workout or exercise patterns
    - Organize project workflows
    - Categorize health and wellness scenarios
    - Create templates for recurring activities
    """,
    response_description="Successfully created use cases with generated IDs and metadata",
    responses={
        200: {
            "description": "Use cases successfully created",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "doc_id": "123e4567-e89b-12d3-a456-************",
                                "name": "Daily Workout Routine",
                                "type": "use_case",
                                "tags": ["fitness", "daily", "routine"],
                                "archived_at": None,
                                "system_properties": {
                                    "created_at": "2024-01-15T08:30:00Z",
                                    "updated_at": "2024-01-15T08:30:00Z",
                                },
                            }
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - duplicate use cases or validation errors",
            "content": {
                "application/json": {
                    "examples": {
                        "duplicate": {
                            "summary": "Duplicate use case",
                            "value": {
                                "detail": "Duplicate use case detected. A use case with the same details already exists in the system."
                            },
                        },
                        "validation": {
                            "summary": "Validation error",
                            "value": {"detail": "Invalid use case data: name is required"},
                        },
                    }
                }
            },
        },
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request body"},
    },
)
async def insert_use_cases_endpoint(
    input_boundary: InsertUseCaseInputBoundary = Depends(InsertUseCaseAPIRequestInput.to_input_boundary),
    use_case: InsertUseCase = Injected(InsertUseCase),
) -> CommonDocumentsResponse[UseCaseAPIOutput]:
    try:
        use_cases = await use_case.execute_async(input_boundary=input_boundary)
    except DuplicateDocumentsFound as err:
        raise BadRequestException(
            message="Duplicate use case detected. A use case with the same details already exists in the system."
        ) from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    return CommonDocumentsResponse[UseCaseAPIOutput](
        documents=[UseCaseAPIOutput(**p.model_dump(by_alias=True)) for p in use_cases]
    )
