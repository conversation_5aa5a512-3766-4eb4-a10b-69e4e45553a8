from typing import Annotated, Sequence
from uuid import UUID

from fastapi import APIRouter, Depends, Query
from fastapi_injector import Injected
from pydantic import StringConstraints
from starlette.responses import StreamingResponse

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.domain.annotated_types import ASSET_ID_REGEX_PATTERN
from services.data_service.api.constants import AssetEndpointRoutes, DataServicePrefixes
from services.data_service.api.models.response.asset.fetch_asset_url_api_output import FetchAssetUrlAPIOutput
from services.data_service.application.use_cases.by_id.fetch_asset_by_id_use_case import FetchAssetByIdUseCase
from services.data_service.application.use_cases.by_id.fetch_asset_url_use_case import FetchAssetUrlUseCase

assets_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.ASSET}",
    tags=["assets"],
    responses={404: {"description": "Not found"}},
)


@assets_router.get(
    AssetEndpointRoutes.BY_ID,
    summary="Fetch Asset by ID",
    description="""
    Retrieve and stream an asset file by its unique identifier.

    This endpoint fetches an asset (such as an image, document, audio, or video file) from the user's
    storage container and streams it back to the client. The asset is returned as a streaming response
    to efficiently handle large files without loading them entirely into memory.

    **Key Features:**
    - **Streaming Response**: Efficient handling of large files through streaming
    - **Access Control**: Only authenticated users can access their own assets
    - **Format Support**: Supports various file types (images, documents, audio, video)
    - **Direct Download**: Returns the raw file content with appropriate media type

    **Query Parameters:**
    - `asset_id`: Unique identifier of the asset to fetch (must match asset ID pattern)

    **Supported Asset Types:**
    - **Images**: JPEG, PNG, GIF, WebP, SVG
    - **Documents**: PDF, TXT, DOC, DOCX
    - **Audio**: MP3, WAV, M4A, OGG
    - **Video**: MP4, MOV, AVI, WebM

    **Use Cases:**
    - Display images in user interfaces
    - Download attached documents
    - Stream audio/video content
    - Access file attachments from events
    """,
    response_description="Streaming response containing the asset file content",
    responses={
        200: {
            "description": "Asset retrieved and streamed successfully",
            "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}},
        },
        400: {"description": "Invalid asset ID format or malformed request"},
        401: {"description": "Authentication required"},
        404: {"description": "Asset not found or access denied"},
    },
    response_class=StreamingResponse,
)
async def fetch_asset_by_id(
    user_uuid: UUID = Depends(get_current_uuid),
    asset_id: str = Query(
        min_length=1, pattern=ASSET_ID_REGEX_PATTERN, description="Unique identifier of the asset to fetch"
    ),
    fetch_assets_use_case: FetchAssetByIdUseCase = Injected(FetchAssetByIdUseCase),
) -> StreamingResponse:
    asset_stream = await fetch_assets_use_case.execute_async(user_uuid=user_uuid, asset_id=asset_id)
    # TODO: media type
    return StreamingResponse(content=asset_stream, media_type="image/jpeg")


@assets_router.get(
    AssetEndpointRoutes.URL,
    summary="Fetch Asset URLs",
    description="""
    Retrieve pre-signed URLs for multiple assets by their identifiers.

    This endpoint generates pre-signed URLs for multiple assets from the user's storage container.
    These URLs provide direct access to the assets without requiring authentication headers,
    making them ideal for frontend applications, batch operations, and temporary access scenarios.

    **Key Features:**
    - **Batch Processing**: Fetch URLs for multiple assets in a single request
    - **Pre-signed URLs**: Direct access URLs with built-in authentication
    - **Temporary Access**: URLs have expiration times for security
    - **Frontend Integration**: Perfect for displaying multiple images or files
    - **No Streaming**: Returns URLs instead of file content for efficiency

    **Query Parameters:**
    - `asset_ids`: Array of asset identifiers to fetch URLs for (each must match asset ID pattern)

    **Use Cases:**
    - Display multiple images in galleries or carousels
    - Batch download operations
    - Frontend applications needing direct file access
    - Temporary sharing of files
    - Mobile applications with offline capabilities

    **URL Characteristics:**
    - **Time-limited**: URLs expire after a set period for security
    - **Direct Access**: No additional authentication required
    - **Original Format**: URLs point to original file format and quality
    - **HTTPS**: All URLs use secure HTTPS protocol
    """,
    response_description="Mapping of asset IDs to their corresponding pre-signed URLs",
    responses={
        200: {
            "description": "Asset URLs retrieved successfully",
            "content": {
                "application/json": {
                    "examples": {
                        "asset_urls_retrieved": {
                            "summary": "Asset URLs retrieved",
                            "value": {
                                "assets": {
                                    "asset_id_1": "https://storage.example.com/user123/asset_id_1?signature=...",
                                    "asset_id_2": "https://storage.example.com/user123/asset_id_2?signature=...",
                                }
                            },
                        }
                    }
                }
            },
        },
        400: {"description": "Invalid asset ID format or malformed request"},
        401: {"description": "Authentication required"},
        404: {"description": "One or more assets not found or access denied"},
    },
)
async def fetch_asset_url(
    user_uuid: UUID = Depends(get_current_uuid),
    asset_ids: Sequence[
        Annotated[
            str,
            StringConstraints(strip_whitespace=True, min_length=1, pattern=ASSET_ID_REGEX_PATTERN),
        ]
    ] = Query(..., description="Array of asset IDs to fetch URLs for"),
    fetch_assets_use_case: FetchAssetUrlUseCase = Injected(FetchAssetUrlUseCase),
) -> FetchAssetUrlAPIOutput:
    output = await fetch_assets_use_case.execute_async(user_uuid=user_uuid, asset_ids=asset_ids)
    return FetchAssetUrlAPIOutput.map(model=output)
