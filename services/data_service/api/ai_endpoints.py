from uuid import UUID

from fastapi import API<PERSON><PERSON><PERSON>, Body, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.data_service.api.constants import AIEndpointRoutes, DataServicePrefixes
from services.data_service.api.models.request.ai.suggest_event_request_input import SuggestEventRequestInput
from services.data_service.application.use_cases.ai.suggest_event_use_case import (
    SuggestEventUseCase,
    SuggestEventUseCaseInputBoundary,
)
from services.data_service.application.use_cases.events.insert_event_inputs import InsertEventInputs

ai_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.AI}",
    tags=["ai"],
    responses={404: {"description": "Not found"}},
)


@ai_router.post(
    AIEndpointRoutes.SUGGEST_EVENT,
    response_model_exclude={"content_hash"},
    summary="Suggest Event from Natural Language",
    description="""
    Generate structured event data from natural language input using AI.

    This endpoint processes a natural language query and returns a structured event that can be
    inserted into the user's timeline. The AI analyzes the query to:

    **Key Features:**
    - **Intelligent Classification**: Automatically determines the appropriate event category
    - **Temporal Extraction**: Identifies timestamps, durations, and end times from natural language
    - **User Context Awareness**: Uses the user's timezone and existing templates for better suggestions
    - **Structured Output**: Returns properly formatted event data ready for insertion
    - **Multi-language Support**: Processes queries in various natural language formats

    **AI Capabilities:**
    - **Event Type Detection**: Classifies as exercise, nutrition, symptom, medication, note, etc.
    - **Time Parsing**: Understands relative times ("yesterday", "this morning", "in 1 hour")
    - **Duration Extraction**: Identifies activity durations and end times
    - **Context Integration**: Leverages user's historical data and preferences
    - **Data Validation**: Ensures generated events meet schema requirements

    **Supported Query Types:**
    - **Nutrition**: "I had a big mac for lunch yesterday", "Ate 2 slices of pizza at 7pm"
    - **Exercise**: "Play football at 10am for 1 hour", "30 minute run in the park"
    - **Symptoms**: "Feeling tired and headache this morning", "Back pain started 2 hours ago"
    - **Medications**: "Took 2 ibuprofen tablets for headache", "Morning vitamins at 8am"
    - **General Notes**: "Had a great meeting with the team", "Feeling stressed about deadline"

    **Response Structure:**
    The response includes all necessary fields to create an event, with timestamps adjusted
    to the user's timezone and content structured according to the detected event type.
    """,
    response_description="Structured event data ready for insertion, with type-specific fields populated based on the natural language input",
    responses={
        200: {
            "description": "Event suggestion generated successfully",
            "content": {
                "application/json": {
                    "examples": [
                        {
                            "summary": "Exercise event suggestion",
                            "value": {
                                "name": "Football",
                                "timestamp": "2024-01-15T10:00:00Z",
                                "end_time": "2024-01-15T11:00:00Z",
                                "type": "exercise",
                                "category": "team_sports",
                                "duration_minutes": 60,
                                "intensity": "moderate",
                            },
                        },
                        {
                            "summary": "Nutrition event suggestion",
                            "value": {
                                "name": "Big Mac",
                                "timestamp": "2024-01-14T12:00:00Z",
                                "type": "nutrition",
                                "category": "fast_food",
                                "meal_type": "lunch",
                                "calories": 550,
                            },
                        },
                    ]
                }
            },
        },
        400: {
            "description": "Bad request - invalid query or unable to parse natural language input",
            "content": {
                "application/json": {
                    "examples": [
                        {
                            "summary": "Unparseable query",
                            "value": {"detail": "Unable to extract meaningful event data from the provided query"},
                        },
                        {
                            "summary": "Invalid input",
                            "value": {"detail": "Query must contain at least one recognizable event or activity"},
                        },
                    ]
                }
            },
        },
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request body"},
        500: {
            "description": "Internal server error - AI service unavailable",
            "content": {
                "application/json": {
                    "examples": [
                        {
                            "summary": "AI service error",
                            "value": {"detail": "AI suggestion service is temporarily unavailable"},
                        }
                    ]
                }
            },
        },
    },
)
async def suggest_event_endpoint(
    input: SuggestEventRequestInput = Body(
        ..., description="Natural language query describing the event to be suggested"
    ),
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: SuggestEventUseCase = Injected(SuggestEventUseCase),
) -> InsertEventInputs:
    return await use_case.execute(
        user_uuid=user_uuid, input_boundary=SuggestEventUseCaseInputBoundary(query=input.query)
    )
