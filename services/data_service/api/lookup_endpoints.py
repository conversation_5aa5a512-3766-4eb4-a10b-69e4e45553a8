import logging
from uuid import UUID

from fastapi import APIRouter, Body, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.application.exceptions import NoContentException
from services.data_service.api.constants import DataServicePrefixes, LookupEndpointRoutes
from services.data_service.api.models.request.content.content_lookup_request_input import (
    PostContentLookupRequestInput,
)
from services.data_service.application.use_cases.content.content_lookup_input_boundary import ContentLookupInputBoundary
from services.data_service.application.use_cases.content.content_lookup_use_case import (
    ContentLookupOutputBoundary,
    ContentLookupUseCase,
)

lookup_router = APIRouter(
    prefix=DataServicePrefixes.V3 + DataServicePrefixes.EXPERIMENTAL_PREFIX + DataServicePrefixes.LOOKUP_PREFIX,
    tags=["lookup"],
    responses={
        404: {"description": "Not found"},
    },
)


@lookup_router.post(
    LookupEndpointRoutes.BASE,
    summary="Extract content metadata from URL",
    description="""
    Extract structured metadata from web content using URL lookup.

    This endpoint analyzes a provided URL and extracts key metadata including title, content type,
    and description. It supports various content types including articles, videos, websites,
    and interactive content. The service attempts to parse standard web metadata (Open Graph,
    Twitter Cards, etc.) and categorizes the content appropriately.

    **Key Features:**
    - **Intelligent Parsing**: Extracts metadata from various web content formats
    - **Content Classification**: Automatically categorizes content by type
    - **Metadata Extraction**: Pulls title, description, and other relevant information
    - **Error Resilience**: Graceful handling of inaccessible or malformed content
    - **Multiple Protocols**: Supports HTTP, HTTPS, and various content delivery networks

    **Supported Content Types:**
    - **Articles**: Blog posts, news articles, documentation, and written content
    - **Videos**: YouTube, Vimeo, and other video platforms with rich metadata
    - **Websites**: General web pages, landing pages, and corporate sites
    - **Interactive**: Web applications, tools, and interactive content platforms
    - **Audio**: Podcasts, music platforms, and audio content
    - **Images**: Image galleries, photos, and visual content platforms

    **Content Analysis Features:**
    - **Automatic Detection**: Identifies content type based on URL patterns and metadata
    - **Title Extraction**: Pulls titles from page metadata, Open Graph, or content analysis
    - **Description Parsing**: Extracts descriptions from meta tags, summaries, or content
    - **Fallback Mechanisms**: Uses multiple strategies when primary metadata is unavailable

    **Metadata Sources:**
    - **Open Graph**: Facebook's Open Graph protocol metadata
    - **Twitter Cards**: Twitter's metadata format for rich content previews
    - **Schema.org**: Structured data markup for enhanced content understanding
    - **HTML Meta Tags**: Standard HTML meta description and title tags
    - **Content Analysis**: Direct content parsing when metadata is insufficient

    **Use Cases:**
    - Link preview generation for social media or messaging applications
    - Content curation and bookmark management
    - Research and reference collection
    - Automated content categorization
    - Rich link sharing in collaborative platforms
    """,
    response_description="Structured content metadata including title, type, and description extracted from the URL",
    responses={
        200: {
            "description": "Content metadata extracted successfully",
            "content": {
                "application/json": {
                    "examples": [
                        {
                            "summary": "Article metadata",
                            "value": {
                                "title": "How to Build Better APIs",
                                "content_type": "article",
                                "description": "A comprehensive guide to designing and implementing robust APIs for modern applications.",
                            },
                        },
                        {
                            "summary": "Video metadata",
                            "value": {
                                "title": "Introduction to Machine Learning",
                                "content_type": "video",
                                "description": "Learn the fundamentals of machine learning in this beginner-friendly tutorial.",
                            },
                        },
                        {
                            "summary": "Website metadata",
                            "value": {
                                "title": "GitHub - user/repository",
                                "content_type": "interactive",
                                "description": "Open source project for building scalable web applications.",
                            },
                        },
                    ]
                }
            },
        },
        204: {
            "description": "No content could be extracted from the URL",
            "content": {
                "application/json": {
                    "examples": [
                        {
                            "summary": "URL inaccessible",
                            "value": None,
                        }
                    ]
                }
            },
        },
        400: {
            "description": "Bad request - invalid URL format or malformed request",
            "content": {
                "application/json": {
                    "examples": [
                        {
                            "summary": "Invalid URL format",
                            "value": {"detail": "Invalid URL format provided"},
                        },
                        {
                            "summary": "Missing URL",
                            "value": {"detail": "URL is required in request body"},
                        },
                    ]
                }
            },
        },
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request body"},
        500: {
            "description": "Internal server error - content lookup service unavailable",
            "content": {
                "application/json": {
                    "examples": [
                        {
                            "summary": "Service error",
                            "value": {"detail": "Content lookup service is temporarily unavailable"},
                        }
                    ]
                }
            },
        },
    },
)
async def post_content_lookup(
    _: UUID = Depends(get_current_uuid),
    body: PostContentLookupRequestInput = Body(
        ...,
        description="URL to analyze and extract content metadata from",
    ),
    use_case: ContentLookupUseCase = Injected(ContentLookupUseCase),
) -> ContentLookupOutputBoundary:
    try:
        return await use_case.execute(input_boundary=ContentLookupInputBoundary(url=body.url))
    except Exception as error:
        # For now let's make everything 204 on error until we do request url validation
        logging.exception(f"could not lookup url: {body.url}, error: {error}")
        raise NoContentException("could not lookup url")
