from uuid import UUI<PERSON>

from fastapi import APIRouter, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.application.exceptions import BadRequestException, IncorrectOperationException, NoContentException
from services.data_service.api.constants import AnalyseEndpointRoutes, DataServicePrefixes
from services.data_service.api.models.output.event_correlation_api_output import EventCorrelationAPIOutput
from services.data_service.api.models.output.suggest_correlation_parameters_api_output import (
    SuggestCorrelationParametersAPIOutput,
)
from services.data_service.api.models.output.trend_detection_api_output import TrendDetectionAPIOutput
from services.data_service.api.models.request.analyze.event_correlation_api_request_input import (
    EventCorrelationAPIRequestInput,
)
from services.data_service.api.models.request.analyze.suggest_correlation_parameters_api_request_input import (
    SuggestCorrelationParametersAPIRequestInput,
)
from services.data_service.api.models.request.analyze.trend_detection_api_request_input import (
    TrendDetectionAPIRequestInput,
)
from services.data_service.application.use_cases.event_correlation.event_correlation_use_case import (
    EventCorrelationUseCase,
    EventCorrelationUseCaseInputBoundary,
)
from services.data_service.application.use_cases.event_correlation.suggest_correlation_parameters_use_case import (
    SuggestCorrelationParametersUseCase,
    SuggestCorrelationParametersUseCaseInputBoundary,
)
from services.data_service.application.use_cases.trend_detection_use_case import (
    TrendDetectionUseCase,
    TrendDetectionUseCaseInputBoundary,
)

analyse_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.ANALYSE}",
    tags=["analyse"],
    responses={404: {"description": "Not found"}},
)


@analyse_router.post(
    AnalyseEndpointRoutes.TREND_DETECT,
    summary="Detect Trends in Time Series Data",
    description="""
    Analyze time series data to detect statistically significant trends using linear regression.

    This endpoint processes a sequence of numerical values to determine if there's a statistically
    significant upward or downward trend. It uses linear regression analysis with configurable
    thresholds for R-squared (model fit quality) and relative slope (trend significance).

    **Key Features:**
    - **Linear Regression Analysis**: Uses statistical methods to identify trends
    - **Configurable Thresholds**: Adjustable sensitivity for trend detection
    - **Statistical Validation**: R-squared and relative slope validation
    - **Trend Classification**: Clear categorization of trend direction
    - **Quantitative Results**: Detailed statistical metrics for analysis

    **Algorithm Process:**
    1. Applies linear regression to the input data series
    2. Calculates R-squared to assess model fit quality
    3. Computes relative slope (coefficient / mean) for trend significance
    4. Classifies trend based on thresholds and statistical significance
    5. Returns comprehensive statistical metrics

    **Use Cases:**
    - **Health Monitoring**: Analyze weight, blood pressure, or symptom trends over time
    - **Symptom Tracking**: Detect patterns in symptom severity or frequency
    - **Environmental Analysis**: Identify trends in environmental data
    - **Performance Metrics**: Track improvement or decline in various measurements
    - **Data Quality**: Validate data consistency and identify anomalies

    **Request Parameters:**
    - `data_series`: Array of numerical values (minimum 2 values required)
    - `r2_threshold`: Minimum R-squared for significant trend (default: 0.3, range: 0.0-1.0)
    - `relative_slope_threshold`: Minimum absolute relative slope (default: 0.01)

    **Response Fields:**
    - `trend_result`: "UPWARD_TREND", "DOWNWARD_TREND", or "NO_TREND"
    - `coefficient`: Linear regression slope coefficient
    - `intercept`: Linear regression y-intercept
    - `relative_slope`: Normalized slope strength (coefficient / mean)
    """,
    response_description="Trend analysis results with statistical metrics and trend classification",
    responses={
        200: {
            "description": "Trend analysis completed successfully",
            "content": {
                "application/json": {
                    "example": {
                        "trend_result": "UPWARD_TREND",
                        "coefficient": 0.25,
                        "intercept": 0.95,
                        "relative_slope": 0.167,
                    }
                }
            },
        },
        400: {
            "description": "Invalid input data or parameters",
            "content": {
                "application/json": {
                    "examples": [
                        {
                            "summary": "Insufficient data points",
                            "value": {"detail": "Data series must contain at least 2 numerical values"},
                        },
                        {
                            "summary": "Invalid threshold values",
                            "value": {"detail": "R-squared threshold must be between 0.0 and 1.0"},
                        },
                    ]
                }
            },
        },
        422: {"description": "Validation error in request body"},
    },
)
async def trend_detection_endpoint(
    input_boundary: TrendDetectionUseCaseInputBoundary = Depends(TrendDetectionAPIRequestInput.to_input_boundary),
    use_case: TrendDetectionUseCase = Injected(TrendDetectionUseCase),
) -> TrendDetectionAPIOutput:
    result = await use_case.execute_async(input_boundary=input_boundary)
    return TrendDetectionAPIOutput.map(model=result)


@analyse_router.post(
    AnalyseEndpointRoutes.CORRELATE_EVENT,
    summary="Analyze Event Correlations",
    description="""
    Perform statistical correlation analysis between two event variables with temporal constraints.

    This endpoint analyzes the statistical relationship between dependent and independent variables
    from user events or environmental data. It automatically selects the appropriate correlation
    method based on data types and provides comprehensive statistical analysis with temporal matching.

    **Key Features:**
    - **Multi-Method Analysis**: Automatically selects appropriate statistical methods
      - Pearson correlation for continuous variables
      - ANOVA F-statistic for continuous vs discrete/binary variables
      - Chi-squared for discrete/binary vs discrete/binary variables
    - **Temporal Matching**: Configurable time windows for event relationships
    - **Data Source Support**: User events and environmental data (weather, air quality, pollen)
    - **Field-Specific Analysis**: Analyze specific fields with aggregation methods
    - **Bias Handling**: Selection bias correction for occurrence-based correlations
    - **Statistical Validation**: P-values, confidence intervals, and significance testing

    **Temporal Relationship Types:**
    - **"before"**: Independent variable occurs before dependent variable
    - **"after"**: Independent variable occurs after dependent variable
    - **"closest"**: Matches closest temporal occurrence regardless of order

    **Supported Data Sources:**
    - **Event Data**: User-generated events (symptoms, medications, activities, etc.)
    - **Environmental Data**: Weather conditions, air quality, pollen counts
    - **Mixed Analysis**: Correlate events with environmental factors

    **Request Structure:**
    - `dependent`: Variable to be predicted/explained
    - `independent`: Variable used for prediction/explanation
    - `temporal_options`: Time-based matching configuration

    **Response Components:**
    - `data`: Value pairs used in correlation analysis
    - `correlation`: Statistical results with coefficient, p-value, and significance
    - `metadata`: Document counts and analysis details
    - `suggested_visualisation`: Recommended chart type for results
    """,
    response_description="Correlation analysis results with statistical metrics and visualization recommendations",
    responses={
        200: {
            "description": "Correlation analysis completed successfully",
            "content": {
                "application/json": {
                    "example": {
                        "data": [[7.5, 1], [8.2, 0], [6.1, 1], [9.0, 0]],
                        "correlation": {
                            "coefficient": -0.65,
                            "p_value": 0.023,
                            "certainty": "moderate",
                            "relationship_strength": "strong",
                        },
                        "dependent": {"document_count": 45},
                        "independent": {"document_count": 23},
                        "suggested_visualisation": "box_plot",
                    }
                }
            },
        },
        400: {
            "description": "Invalid query parameters or temporal options",
            "content": {
                "application/json": {
                    "examples": [
                        {
                            "summary": "Invalid query structure",
                            "value": {"detail": "Invalid event query: missing required field 'types'"},
                        },
                        {
                            "summary": "Invalid temporal options",
                            "value": {
                                "detail": "Temporal interval must be positive and in valid format (e.g., '4h', '2d')"
                            },
                        },
                    ]
                }
            },
        },
        204: {
            "description": "No matching data found for correlation analysis",
            "content": {
                "application/json": {
                    "example": {"detail": "No data available for correlation analysis with the specified parameters"}
                }
            },
        },
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request body"},
    },
)
async def event_correlation_endpoint(
    input_boundary: EventCorrelationUseCaseInputBoundary = Depends(EventCorrelationAPIRequestInput.to_input_boundary),
    use_case: EventCorrelationUseCase = Injected(EventCorrelationUseCase),
) -> EventCorrelationAPIOutput:
    try:
        result = await use_case.execute_async(input_boundary=input_boundary)
        if not result.data:
            raise NoContentException("No data available")
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    except NoContentException as err:
        raise NoContentException(message=err.message) from err
    return EventCorrelationAPIOutput.map(model=result)


@analyse_router.post(
    AnalyseEndpointRoutes.CORRELATE_EVENT_SUGGEST_PARAMETERS,
    summary="Get AI-Powered Correlation Parameter Suggestions",
    description="""
    Receive intelligent recommendations for optimal correlation analysis parameters using AI.

    This endpoint leverages artificial intelligence to analyze provided event queries and suggest
    the most appropriate parameters for running correlation analysis. The AI considers event types,
    their typical relationships, domain knowledge, and best practices to recommend optimal settings
    for meaningful statistical analysis.

    **Key Features:**
    - **Intelligent Analysis**: AI examines event types and schemas to understand data characteristics
    - **Domain Knowledge**: Incorporates understanding of typical relationships between event categories
    - **Optimal Parameters**: Suggests appropriate aggregation methods, temporal windows, and field selections
    - **Reasoning Provided**: Detailed explanations for all recommendations
    - **Learning Tool**: Helps users understand correlation analysis best practices

    **AI Analysis Process:**
    1. **Schema Analysis**: Examines event types and available fields
    2. **Relationship Modeling**: Considers typical temporal relationships between event categories
    3. **Method Selection**: Suggests appropriate aggregation methods based on data characteristics
    4. **Temporal Optimization**: Recommends optimal time windows for meaningful correlations
    5. **Validation**: Ensures suggested parameters will produce statistically valid results

    **Use Cases:**
    - **Getting Started**: Ideal for users new to correlation analysis
    - **Parameter Validation**: Verify manual parameter choices against AI recommendations
    - **Learning**: Understand typical relationships between different event types
    - **Automation**: Streamline correlation setup for common analysis patterns
    - **Best Practices**: Learn optimal parameter selection for various scenarios

    **Request Structure:**
    - `dependent_query`: Query defining the dependent variable events
    - `independent_query`: Query defining the independent variable events

    **Response Components:**
    - `dependent`/`independent`: Suggested configurations with field names and aggregation methods
    - `temporal_options`: Recommended temporal matching settings
    - `reasoning`: Comprehensive explanation of all suggestions
    """,
    response_description="AI-generated parameter suggestions with detailed reasoning for correlation analysis",
    responses={
        200: {
            "description": "Parameter suggestions generated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "dependent": {
                            "field_name": "rating",
                            "aggregation_method": "mean",
                            "query": {"types": ["symptom"], "filters": {"name": "headache"}},
                        },
                        "independent": {
                            "field_name": None,
                            "aggregation_method": None,
                            "query": {"types": ["food"], "filters": {"category": "caffeine"}},
                        },
                        "temporal_options": {
                            "type": "before",
                            "time_input": {"interval": "4h", "time_gte": "2024-01-01", "time_lte": "2024-01-31"},
                        },
                        "reasoning": "Suggesting 'before' with '4h' interval as caffeine intake often precedes headache onset within hours. Using 'rating' field for headache severity provides more nuanced analysis than simple occurrence counting.",
                    }
                }
            },
        },
        204: {
            "description": "AI could not generate suggestions for the provided queries",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Unable to generate correlation parameter suggestions for the specified event types"
                    }
                }
            },
        },
        400: {
            "description": "Invalid query structure or unsupported event types",
            "content": {
                "application/json": {
                    "example": {"detail": "Invalid event query: unsupported event type or malformed query structure"}
                }
            },
        },
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request body"},
    },
)
async def suggest_correlation_parameters_endpoint(
    _: UUID = Depends(get_current_uuid),
    input_boundary: SuggestCorrelationParametersUseCaseInputBoundary = Depends(
        SuggestCorrelationParametersAPIRequestInput.to_input_boundary
    ),
    use_case: SuggestCorrelationParametersUseCase = Injected(SuggestCorrelationParametersUseCase),
) -> SuggestCorrelationParametersAPIOutput:
    result = await use_case.execute_async(input_boundary=input_boundary)
    if not result:
        raise NoContentException("No suggestion available")
    return SuggestCorrelationParametersAPIOutput.map(model=result)
